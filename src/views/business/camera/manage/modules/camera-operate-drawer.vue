<script setup lang="ts">
import { computed, reactive, watch } from 'vue';
import { NDivider, NFormItemGi, NGrid, NInputNumber } from 'naive-ui';
import { fetchCreateCamera, fetchUpdateCamera } from '@/service/api/business/camera';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { $t } from '@/locales';

defineOptions({
  name: 'CameraOperateDrawer'
});

interface Props {
  /** the type of operation */
  operateType: NaiveUI.TableOperateType;
  /** the edit row data */
  rowData?: Api.Business.Camera | null;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});

const { formRef, validate, restoreValidation } = useNaiveForm();
const { createRequiredRule } = useFormRules();

const title = computed(() => {
  const titles: Record<NaiveUI.TableOperateType, string> = {
    add: '新增摄像头',
    edit: '编辑摄像头'
  };
  return titles[props.operateType];
});

type Model = Api.Business.CameraOperateParams;

const model: Model = reactive(createDefaultModel());

// AI功能数组，用于多选组件
const aiFunctionsArray = computed(() => {
  if (!model.aiFunctions) return [];
  return model.aiFunctions.split(',').filter(item => item.trim());
});

// 处理AI功能变化
function handleAiFunctionsChange(values: string[]) {
  model.aiFunctions = values.join(',');
}

function createDefaultModel(): Model {
  return {
    cameraNumber: undefined,
    cameraName: undefined,
    buildingNumber: undefined,
    floorNumber: undefined,
    deviceModel: undefined,
    installDate: undefined,
    alertCount: undefined,
    soundColumnId: undefined,
    signalStrength: undefined,
    cameraLocation: undefined,
    ipAddress: undefined,
    loginUsername: undefined,
    loginPassword: undefined,
    cameraStatus: undefined,
    lastOnlineTime: undefined,
    resolution: undefined,
    nightVision: undefined,
    viewingAngle: undefined,
    aiFunctions: undefined,
    storageMethod: undefined,
    dataEncryption: undefined,
    streamPullUrl: undefined,
    remark: undefined
  };
}

type RuleKey = Extract<
  keyof Model,
  | 'cameraNumber'
  | 'cameraName'
  | 'buildingNumber'
  | 'floorNumber'
  | 'cameraLocation'
  | 'deviceModel'
  | 'ipAddress'
  | 'loginUsername'
  | 'loginPassword'
  | 'cameraStatus'
  | 'signalStrength'
>;

const rules: Record<RuleKey, App.Global.FormRule | App.Global.FormRule[]> = {
  cameraNumber: [
    createRequiredRule('摄像头编号不能为空')
    // {
    //   pattern: /^CAM-[A-Z0-9]+-\d+-\d+$/,
    //   message: '摄像头编号格式不正确，应为：CAM-区域-楼栋-序号',
    //   trigger: ['input', 'blur']
    // }
  ],
  cameraName: createRequiredRule('摄像头名称不能为空'),
  buildingNumber: createRequiredRule('楼栋号不能为空'),
  floorNumber: createRequiredRule('楼层号不能为空'),
  cameraLocation: createRequiredRule('安装位置不能为空'),
  deviceModel: createRequiredRule('设备型号不能为空'),
  ipAddress: [
    createRequiredRule('IP地址不能为空'),
    {
      pattern: /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
      message: 'IP地址格式不正确',
      trigger: ['input', 'blur']
    }
  ],
  loginUsername: [
    createRequiredRule('登录用户名不能为空'),
    {
      min: 3,
      max: 20,
      message: '用户名长度应在3-20个字符之间',
      trigger: ['input', 'blur']
    }
  ],
  loginPassword: [
    createRequiredRule('登录密码不能为空'),
    {
      min: 6,
      message: '密码长度不能少于6个字符',
      trigger: ['input', 'blur']
    }
  ],
  cameraStatus: createRequiredRule('设备状态不能为空'),
  signalStrength: createRequiredRule('信号强度不能为空')
};

function handleUpdateModelWhenEdit() {
  if (props.operateType === 'add') {
    Object.assign(model, createDefaultModel());
    return;
  }

  if (props.operateType === 'edit' && props.rowData) {
    Object.assign(model, props.rowData);
    // 确保 aiFunctions 是字符串格式
    if (Array.isArray(model.aiFunctions)) {
      model.aiFunctions = model.aiFunctions.join(',');
    }
  }
}

function closeDrawer() {
  visible.value = false;
}

async function handleSubmit() {
  await validate();

  const {
    id,
    cameraNumber,
    cameraName,
    buildingNumber,
    floorNumber,
    deviceModel,
    installDate,
    alertCount,
    soundColumnId,
    signalStrength,
    cameraLocation,
    ipAddress,
    loginUsername,
    loginPassword,
    cameraStatus,
    lastOnlineTime,
    resolution,
    nightVision,
    viewingAngle,
    aiFunctions,
    storageMethod,
    dataEncryption,
    streamPullUrl,
    remark
  } = model;

  // request
  if (props.operateType === 'add') {
    const { error } = await fetchCreateCamera({
      cameraNumber,
      cameraName,
      buildingNumber,
      floorNumber,
      deviceModel,
      installDate,
      alertCount,
      soundColumnId,
      signalStrength,
      cameraLocation,
      ipAddress,
      loginUsername,
      loginPassword,
      cameraStatus,
      lastOnlineTime,
      resolution,
      nightVision,
      viewingAngle,
      aiFunctions,
      storageMethod,
      dataEncryption,
      streamPullUrl,
      remark
    });
    if (error) return;
  }

  if (props.operateType === 'edit') {
    const { error } = await fetchUpdateCamera({
      id,
      cameraNumber,
      cameraName,
      buildingNumber,
      floorNumber,
      deviceModel,
      installDate,
      alertCount,
      soundColumnId,
      signalStrength,
      cameraLocation,
      ipAddress,
      loginUsername,
      loginPassword,
      cameraStatus,
      lastOnlineTime,
      resolution,
      nightVision,
      viewingAngle,
      aiFunctions,
      storageMethod,
      dataEncryption,
      streamPullUrl,
      remark
    });
    if (error) return;
  }

  window.$message?.success($t('common.updateSuccess'));
  closeDrawer();
  emit('submitted');
}

watch(visible, () => {
  if (visible.value) {
    handleUpdateModelWhenEdit();
    restoreValidation();
  }
});
</script>

<template>
  <NDrawer
    v-model:show="visible"
    :title="title"
    :mask-closable="false"
    display-directive="show"
    :width="800"
    class="max-w-90%"
  >
    <NDrawerContent :title="title" :native-scrollbar="false" closable>
      <NForm ref="formRef" :model="model" :rules="rules">
        <!-- 基本信息 -->
        <NDivider title-placement="left">基本信息</NDivider>
        <NFormItem label="摄像头编号" path="cameraNumber">
          <NInput v-model:value="model.cameraNumber" placeholder="格式：CAM-区域-楼栋-序号，如CAM-A01-1-001" />
        </NFormItem>
        <NFormItem label="摄像头名称" path="cameraName">
          <NInput v-model:value="model.cameraName" placeholder="请输入摄像头名称" />
        </NFormItem>
        <NGrid :cols="2" :x-gap="16">
          <NFormItemGi label="楼栋号" path="buildingNumber">
            <NInput v-model:value="model.buildingNumber" placeholder="如1号楼、A栋" />
          </NFormItemGi>
          <NFormItemGi label="楼层号" path="floorNumber">
            <NInput v-model:value="model.floorNumber" placeholder="如1层、地下1层" />
          </NFormItemGi>
        </NGrid>
        <NFormItem label="安装位置" path="cameraLocation">
          <NInput v-model:value="model.cameraLocation" placeholder="请输入摄像头具体安装位置" />
        </NFormItem>
        <NGrid :cols="2" :x-gap="16">
          <NFormItemGi label="设备状态" path="cameraStatus">
            <NSelect
              v-model:value="model.cameraStatus"
              placeholder="请选择设备状态"
              :options="[
                { label: '在线', value: 'online' },
                { label: '离线', value: 'offline' },
                { label: '维护中', value: 'maintenance' }
              ]"
            />
          </NFormItemGi>
          <NFormItemGi label="信号强度" path="signalStrength">
            <NSelect
              v-model:value="model.signalStrength"
              placeholder="请选择信号强度"
              :options="[
                { label: '强', value: 'strong' },
                { label: '中', value: 'medium' },
                { label: '弱', value: 'weak' }
              ]"
            />
          </NFormItemGi>
        </NGrid>

        <!-- 设备信息 -->
        <NDivider title-placement="left">设备信息</NDivider>

        <NGrid :cols="2" :x-gap="16">
          <NFormItemGi label="设备型号" path="deviceModel">
            <NInput v-model:value="model.deviceModel" placeholder="如海康威视DS-2CD3T46WDV3-I3" />
          </NFormItemGi>
          <NFormItemGi label="安装日期" path="installDate">
            <NDatePicker v-model:formatted-value="model.installDate" type="date" value-format="yyyy-MM-dd" clearable />
          </NFormItemGi>
        </NGrid>
        <!--
 <NFormItemGi label="报警次数" path="alertCount">
          <NInputNumber v-model:value="model.alertCount" placeholder="累计报警次数" :min="0" />
        </NFormItemGi>
-->

        <!-- 网络配置 -->
        <NDivider title-placement="left">网络配置</NDivider>
        <NGrid :cols="2" :x-gap="16">
          <NFormItemGi label="IP地址" path="ipAddress">
            <NInput v-model:value="model.ipAddress" placeholder="如*************" />
          </NFormItemGi>
          <NFormItemGi label="拉流地址" path="streamPullUrl">
            <NInput v-model:value="model.streamPullUrl" placeholder="RTSP/RTMP协议地址" />
          </NFormItemGi>
        </NGrid>
        <NGrid :cols="2" :x-gap="16">
          <NFormItemGi label="登录用户名" path="loginUsername">
            <NInput v-model:value="model.loginUsername" placeholder="请输入登录用户名" />
          </NFormItemGi>
          <NFormItemGi label="登录密码" path="loginPassword">
            <NInput
              v-model:value="model.loginPassword"
              type="password"
              placeholder="请输入登录密码"
              show-password-on="click"
            />
          </NFormItemGi>
        </NGrid>

        <!-- 技术参数 -->
        <NDivider title-placement="left">技术参数</NDivider>
        <NGrid :cols="2" :x-gap="16">
          <NFormItemGi label="分辨率" path="resolution">
            <NSelect
              v-model:value="model.resolution"
              placeholder="请选择分辨率"
              :options="[
                { label: '1920×1080 (1080P)', value: '1920x1080' },
                { label: '2560×1440 (2K)', value: '2560x1440' },
                { label: '3840×2160 (4K)', value: '3840x2160' }
              ]"
            />
          </NFormItemGi>
          <NFormItemGi label="夜视功能" path="nightVision">
            <NSelect
              v-model:value="model.nightVision"
              placeholder="请选择夜视功能"
              :options="[
                { label: '支持', value: 'supported' },
                { label: '不支持', value: 'not_supported' }
              ]"
            />
          </NFormItemGi>
        </NGrid>
        <NGrid :cols="2" :x-gap="16">
          <NFormItemGi label="视角范围" path="viewingAngle">
            <NInput v-model:value="model.viewingAngle" placeholder="如水平90°、垂直60°" />
          </NFormItemGi>
          <NFormItemGi label="存储方式" path="storageMethod">
            <NSelect
              v-model:value="model.storageMethod"
              placeholder="请选择存储方式"
              :options="[
                { label: '本地存储', value: 'local' },
                { label: '云存储', value: 'cloud' },
                { label: 'NVR集中存储', value: 'nvr' }
              ]"
            />
          </NFormItemGi>
        </NGrid>
        <NGrid :cols="2" :x-gap="16">
          <NFormItemGi label="数据加密" path="dataEncryption">
            <NSelect
              v-model:value="model.dataEncryption"
              placeholder="请选择加密状态"
              :options="[
                { label: '已加密', value: 'encrypted' },
                { label: '未加密', value: 'not_encrypted' }
              ]"
            />
          </NFormItemGi>
          <NFormItemGi label="关联音柱" path="soundColumnId">
            <NInput
              :value="String(model.soundColumnId || '')"
              placeholder="关联音柱ID"
              @update:value="val => (model.soundColumnId = val ? Number(val) : undefined)"
            />
          </NFormItemGi>
        </NGrid>

        <!-- AI功能 -->
        <NFormItem label="AI功能" path="aiFunctions">
          <NSelect
            :value="aiFunctionsArray"
            placeholder="请选择AI功能"
            multiple
            :options="[
              { label: '人脸识别', value: 'face_recognition' },
              { label: '行为分析', value: 'behavior_analysis' },
              { label: '区域入侵检测', value: 'intrusion_detection' },
              { label: '车牌识别', value: 'license_plate_recognition' },
              { label: '人员计数', value: 'people_counting' },
              { label: '异常行为检测', value: 'abnormal_behavior_detection' }
            ]"
            @update:value="handleAiFunctionsChange"
          />
        </NFormItem>

        <!-- 备注信息 -->
        <NFormItem label="备注信息" path="remark">
          <NInput
            v-model:value="model.remark"
            :rows="3"
            type="textarea"
            placeholder="请输入备注信息，如安装高度、负责区域等"
          />
        </NFormItem>
      </NForm>
      <template #footer>
        <NSpace :size="16">
          <NButton @click="closeDrawer">{{ $t('common.cancel') }}</NButton>
          <NButton type="primary" @click="handleSubmit">{{ $t('common.confirm') }}</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
